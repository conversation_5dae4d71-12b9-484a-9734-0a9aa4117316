import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { COLORS } from '@/constants';

export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: COLORS.primary,
    primaryContainer: COLORS.primaryDark,
    secondary: COLORS.secondary,
    secondaryContainer: COLORS.secondary + '20',
    tertiary: COLORS.accent,
    tertiaryContainer: COLORS.accent + '20',
    surface: COLORS.surface,
    surfaceVariant: COLORS.background,
    background: COLORS.background,
    error: COLORS.error,
    errorContainer: COLORS.error + '20',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#FFFFFF',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: COLORS.text,
    onTertiary: '#FFFFFF',
    onTertiaryContainer: COLORS.text,
    onSurface: COLORS.text,
    onSurfaceVariant: COLORS.textSecondary,
    onBackground: COLORS.text,
    onError: '#FFFFFF',
    onErrorContainer: COLORS.error,
    outline: COLORS.border,
    outlineVariant: COLORS.border + '80',
    inverseSurface: COLORS.text,
    inverseOnSurface: COLORS.surface,
    inversePrimary: COLORS.primary + '80',
    shadow: '#000000',
    scrim: '#000000',
    backdrop: 'rgba(0, 0, 0, 0.5)',
  },
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: COLORS.primary,
    primaryContainer: COLORS.primaryDark,
    secondary: COLORS.secondary,
    secondaryContainer: COLORS.secondary + '20',
    tertiary: COLORS.accent,
    tertiaryContainer: COLORS.accent + '20',
    surface: '#1E1E1E',
    surfaceVariant: '#2E2E2E',
    background: '#121212',
    error: COLORS.error,
    errorContainer: COLORS.error + '20',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#FFFFFF',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#FFFFFF',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#FFFFFF',
    onSurface: '#FFFFFF',
    onSurfaceVariant: '#CCCCCC',
    onBackground: '#FFFFFF',
    onError: '#FFFFFF',
    onErrorContainer: COLORS.error,
    outline: '#666666',
    outlineVariant: '#444444',
    inverseSurface: '#FFFFFF',
    inverseOnSurface: '#121212',
    inversePrimary: COLORS.primary + '80',
    shadow: '#000000',
    scrim: '#000000',
    backdrop: 'rgba(0, 0, 0, 0.7)',
  },
};

export const theme = lightTheme; // Default to light theme
