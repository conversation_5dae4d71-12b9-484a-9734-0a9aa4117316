import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  Searchbar,
  FAB,
  useTheme,
  ActivityIndicator,
  Chip
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';

import { RootState, AppDispatch } from '@/store';
import { fetchProducts, searchProducts, clearSearchResults } from '@/store/slices/productSlice';
import { addToCart } from '@/store/slices/cartSlice';
import { Product } from '@/types';
import { DEFAULTS } from '@/constants';

const ProductsScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  
  const { data: products, searchResults, isLoading } = useSelector((state: RootState) => state.products);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(fetchProducts({ limit: 50 }));
  }, [dispatch]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      dispatch(searchProducts(query));
    } else {
      dispatch(clearSearchResults());
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchProducts({ limit: 50 }));
    setRefreshing(false);
  };

  const handleAddToCart = (product: Product) => {
    dispatch(addToCart({ product, quantity: 1 }));
  };

  const displayProducts = searchQuery.trim() ? searchResults : (products || []);

  const renderProduct = ({ item }: { item: Product }) => (
    <Card style={styles.productCard}>
      <Card.Content>
        <View style={styles.productHeader}>
          <Text variant="titleMedium" style={styles.productName}>
            {item.name}
          </Text>
          <Chip 
            mode="outlined" 
            compact
            style={[styles.typeChip, { borderColor: theme.colors.primary }]}
          >
            {item.type}
          </Chip>
        </View>
        
        {item.description && (
          <Text variant="bodySmall" style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
            {item.description}
          </Text>
        )}
        
        <View style={styles.productDetails}>
          <Text variant="headlineSmall" style={[styles.price, { color: theme.colors.primary }]}>
            {DEFAULTS.CURRENCY}{item.price.toFixed(2)}
          </Text>
          
          <View style={styles.stockInfo}>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Stock: {item.stockQuantity}
            </Text>
            {item.stockQuantity <= item.minStockLevel && (
              <Chip 
                mode="flat" 
                compact
                style={[styles.lowStockChip, { backgroundColor: theme.colors.errorContainer }]}
                textStyle={{ color: theme.colors.onErrorContainer }}
              >
                Low Stock
              </Chip>
            )}
          </View>
        </View>
        
        <Button
          mode="contained"
          onPress={() => handleAddToCart(item)}
          disabled={item.stockQuantity === 0}
          style={styles.addButton}
          icon="cart-plus"
        >
          {item.stockQuantity === 0 ? 'Out of Stock' : 'Add to Cart'}
        </Button>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <Searchbar
          placeholder="Search products..."
          onChangeText={handleSearch}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        {isLoading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text variant="bodyMedium" style={styles.loadingText}>
              Loading products...
            </Text>
          </View>
        ) : (
          <FlatList
            data={displayProducts}
            renderItem={renderProduct}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[theme.colors.primary]}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text variant="bodyLarge" style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
                  {searchQuery.trim() ? 'No products found' : 'No products available'}
                </Text>
              </View>
            }
          />
        )}
      </View>
      
      <FAB
        icon="barcode-scan"
        style={styles.fab}
        onPress={() => {/* Open barcode scanner */}}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchbar: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  listContent: {
    paddingBottom: 80, // Space for FAB
  },
  productCard: {
    marginBottom: 12,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  productName: {
    flex: 1,
    fontWeight: 'bold',
    marginRight: 8,
  },
  typeChip: {
    height: 24,
  },
  description: {
    marginBottom: 12,
    lineHeight: 20,
  },
  productDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  price: {
    fontWeight: 'bold',
  },
  stockInfo: {
    alignItems: 'flex-end',
  },
  lowStockChip: {
    marginTop: 4,
    height: 20,
  },
  addButton: {
    marginTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default ProductsScreen;
