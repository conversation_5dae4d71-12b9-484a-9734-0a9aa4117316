package com.rcpos.app.data.api

import com.rcpos.app.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface OrderApiService {
    
    @GET("orders")
    suspend fun getOrders(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20,
        @Query("search") search: String? = null,
        @Query("status") status: OrderStatus? = null,
        @Query("customerId") customerId: String? = null,
        @Query("userId") userId: String? = null,
        @Query("startDate") startDate: String? = null,
        @Query("endDate") endDate: String? = null,
        @Query("minAmount") minAmount: Double? = null,
        @Query("maxAmount") maxAmount: Double? = null
    ): Response<ApiResponse<PaginatedResponse<Order>>>
    
    @GET("orders/{id}")
    suspend fun getOrderById(@Path("id") id: String): Response<ApiResponse<Order>>
    
    @POST("orders")
    suspend fun createOrder(@Body request: CreateOrderRequest): Response<ApiResponse<Order>>
    
    @PUT("orders/{id}")
    suspend fun updateOrder(
        @Path("id") id: String,
        @Body request: UpdateOrderRequest
    ): Response<ApiResponse<Order>>
    
    @POST("orders/{orderId}/payments")
    suspend fun createPayment(
        @Path("orderId") orderId: String,
        @Body request: CreatePaymentRequest
    ): Response<ApiResponse<Payment>>
    
    @PUT("orders/{orderId}/payments/{paymentId}")
    suspend fun updatePayment(
        @Path("orderId") orderId: String,
        @Path("paymentId") paymentId: String,
        @Body request: UpdatePaymentRequest
    ): Response<ApiResponse<Payment>>
}
