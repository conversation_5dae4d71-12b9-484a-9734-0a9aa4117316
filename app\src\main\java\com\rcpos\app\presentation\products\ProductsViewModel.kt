package com.rcpos.app.presentation.products

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.rcpos.app.data.model.NetworkResult
import com.rcpos.app.data.model.Product
import com.rcpos.app.data.model.ProductQuery
import com.rcpos.app.data.model.UiState
import com.rcpos.app.data.repository.CartRepository
import com.rcpos.app.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductsViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val cartRepository: CartRepository
) : ViewModel() {
    
    private val _productsState = MutableStateFlow(UiState<List<Product>>())
    val productsState: StateFlow<UiState<List<Product>>> = _productsState.asStateFlow()
    
    val cartItemCount = cartRepository.cartItemCount
    
    init {
        // Observe local products for offline support
        viewModelScope.launch {
            productRepository.getAllProducts().collectLatest { products ->
                if (_productsState.value.data.isNullOrEmpty() && products.isNotEmpty()) {
                    _productsState.value = UiState(data = products)
                }
            }
        }
    }
    
    fun loadProducts() {
        viewModelScope.launch {
            _productsState.value = UiState(isLoading = true)
            
            when (val result = productRepository.fetchProducts(ProductQuery(limit = 50))) {
                is NetworkResult.Success -> {
                    _productsState.value = UiState(data = result.data.items)
                }
                is NetworkResult.Error -> {
                    // Try to load from local cache
                    productRepository.getAllProducts().collectLatest { localProducts ->
                        _productsState.value = UiState(
                            data = localProducts,
                            error = if (localProducts.isEmpty()) result.message else null
                        )
                    }
                }
                is NetworkResult.Loading -> {
                    _productsState.value = UiState(isLoading = true)
                }
            }
        }
    }
    
    fun searchProducts(query: String) {
        viewModelScope.launch {
            _productsState.value = UiState(isLoading = true)
            
            // First try API search
            when (val result = productRepository.fetchProducts(ProductQuery(search = query, limit = 50))) {
                is NetworkResult.Success -> {
                    _productsState.value = UiState(data = result.data.items)
                }
                is NetworkResult.Error -> {
                    // Fallback to local search
                    productRepository.searchProducts(query).collectLatest { localProducts ->
                        _productsState.value = UiState(data = localProducts)
                    }
                }
                is NetworkResult.Loading -> {
                    _productsState.value = UiState(isLoading = true)
                }
            }
        }
    }
    
    fun addToCart(product: Product, quantity: Int = 1, notes: String? = null) {
        cartRepository.addToCart(product, quantity, notes)
    }
    
    fun isInCart(productId: String): Boolean {
        return cartRepository.isInCart(productId)
    }
    
    fun refreshProducts() {
        loadProducts()
    }
}
