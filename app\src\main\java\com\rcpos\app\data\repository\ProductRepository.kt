package com.rcpos.app.data.repository

import com.rcpos.app.data.api.ProductApiService
import com.rcpos.app.data.local.dao.ProductDao
import com.rcpos.app.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ProductRepository @Inject constructor(
    private val productApiService: ProductApiService,
    private val productDao: ProductDao
) {
    
    fun getAllProducts(): Flow<List<Product>> = productDao.getAllProducts()
    
    fun searchProducts(query: String): Flow<List<Product>> = productDao.searchProducts(query)
    
    fun getProductsByCategory(categoryId: String): Flow<List<Product>> = 
        productDao.getProductsByCategory(categoryId)
    
    fun getProductsByType(type: ProductType): Flow<List<Product>> = 
        productDao.getProductsByType(type)
    
    fun getLowStockProducts(): Flow<List<Product>> = productDao.getLowStockProducts()
    
    suspend fun getProductById(id: String): Product? = productDao.getProductById(id)
    
    suspend fun getProductByBarcode(barcode: String): Product? = 
        productDao.getProductByBarcode(barcode)
    
    suspend fun getProductBySku(sku: String): Product? = productDao.getProductBySku(sku)
    
    suspend fun fetchProducts(query: ProductQuery = ProductQuery()): NetworkResult<PaginatedResponse<Product>> {
        return try {
            val response = productApiService.getProducts(
                page = query.page,
                limit = query.limit,
                search = query.search,
                categoryId = query.categoryId,
                type = query.type,
                isActive = query.isActive,
                lowStock = query.lowStock
            )
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    // Cache products locally
                    if (query.page == 1) {
                        // If it's the first page, replace all products
                        productDao.deleteAllProducts()
                    }
                    productDao.insertProducts(apiResponse.data.items)
                    
                    NetworkResult.Success(apiResponse.data)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Failed to fetch products")
                }
            } else {
                NetworkResult.Error("Failed to fetch products: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun fetchProductById(id: String): NetworkResult<Product> {
        return try {
            val response = productApiService.getProductById(id)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    // Cache product locally
                    productDao.insertProduct(apiResponse.data)
                    NetworkResult.Success(apiResponse.data)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Product not found")
                }
            } else {
                NetworkResult.Error("Failed to fetch product: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun createProduct(request: CreateProductRequest): NetworkResult<Product> {
        return try {
            val response = productApiService.createProduct(request)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    // Cache new product locally
                    productDao.insertProduct(apiResponse.data)
                    NetworkResult.Success(apiResponse.data)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Failed to create product")
                }
            } else {
                NetworkResult.Error("Failed to create product: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun updateProduct(id: String, request: UpdateProductRequest): NetworkResult<Product> {
        return try {
            val response = productApiService.updateProduct(id, request)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    // Update product in local cache
                    productDao.insertProduct(apiResponse.data)
                    NetworkResult.Success(apiResponse.data)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Failed to update product")
                }
            } else {
                NetworkResult.Error("Failed to update product: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun deleteProduct(id: String): NetworkResult<Unit> {
        return try {
            val response = productApiService.deleteProduct(id)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true) {
                    // Remove product from local cache
                    productDao.deleteProductById(id)
                    NetworkResult.Success(Unit)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Failed to delete product")
                }
            } else {
                NetworkResult.Error("Failed to delete product: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun updateStock(id: String, request: UpdateStockRequest): NetworkResult<Product> {
        return try {
            val response = productApiService.updateStock(id, request)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    // Update product in local cache
                    productDao.insertProduct(apiResponse.data)
                    NetworkResult.Success(apiResponse.data)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Failed to update stock")
                }
            } else {
                NetworkResult.Error("Failed to update stock: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun getProductCount(): Int = productDao.getProductCount()
    
    suspend fun getLowStockCount(): Int = productDao.getLowStockCount()
}
