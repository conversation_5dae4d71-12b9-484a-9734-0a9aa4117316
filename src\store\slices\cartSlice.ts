import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CartItem, Product } from '@/types';
import { DEFAULTS } from '@/constants';

interface CartState {
  items: CartItem[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  total: number;
  itemCount: number;
}

const initialState: CartState = {
  items: [],
  subtotal: 0,
  taxAmount: 0,
  discountAmount: 0,
  total: 0,
  itemCount: 0,
};

const calculateTotals = (items: CartItem[], discountAmount: number = 0) => {
  const subtotal = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const taxAmount = subtotal * DEFAULTS.TAX_RATE;
  const total = subtotal + taxAmount - discountAmount;
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
  
  return { subtotal, taxAmount, total, itemCount };
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<{ product: Product; quantity?: number; notes?: string }>) => {
      const { product, quantity = 1, notes } = action.payload;
      const existingItemIndex = state.items.findIndex(item => item.product.id === product.id);
      
      if (existingItemIndex !== -1) {
        // Update existing item
        state.items[existingItemIndex].quantity += quantity;
        if (notes) {
          state.items[existingItemIndex].notes = notes;
        }
      } else {
        // Add new item
        state.items.push({ product, quantity, notes });
      }
      
      const totals = calculateTotals(state.items, state.discountAmount);
      Object.assign(state, totals);
    },
    
    removeFromCart: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      state.items = state.items.filter(item => item.product.id !== productId);
      
      const totals = calculateTotals(state.items, state.discountAmount);
      Object.assign(state, totals);
    },
    
    updateQuantity: (state, action: PayloadAction<{ productId: string; quantity: number }>) => {
      const { productId, quantity } = action.payload;
      
      if (quantity <= 0) {
        state.items = state.items.filter(item => item.product.id !== productId);
      } else {
        const itemIndex = state.items.findIndex(item => item.product.id === productId);
        if (itemIndex !== -1) {
          state.items[itemIndex].quantity = quantity;
        }
      }
      
      const totals = calculateTotals(state.items, state.discountAmount);
      Object.assign(state, totals);
    },
    
    updateNotes: (state, action: PayloadAction<{ productId: string; notes?: string }>) => {
      const { productId, notes } = action.payload;
      const itemIndex = state.items.findIndex(item => item.product.id === productId);
      
      if (itemIndex !== -1) {
        state.items[itemIndex].notes = notes;
      }
    },
    
    applyDiscount: (state, action: PayloadAction<number>) => {
      state.discountAmount = action.payload;
      
      const totals = calculateTotals(state.items, state.discountAmount);
      Object.assign(state, totals);
    },
    
    clearCart: (state) => {
      state.items = [];
      state.subtotal = 0;
      state.taxAmount = 0;
      state.discountAmount = 0;
      state.total = 0;
      state.itemCount = 0;
    },
    
    recalculateTotals: (state) => {
      const totals = calculateTotals(state.items, state.discountAmount);
      Object.assign(state, totals);
    },
  },
});

export const {
  addToCart,
  removeFromCart,
  updateQuantity,
  updateNotes,
  applyDiscount,
  clearCart,
  recalculateTotals,
} = cartSlice.actions;

export default cartSlice.reducer;
