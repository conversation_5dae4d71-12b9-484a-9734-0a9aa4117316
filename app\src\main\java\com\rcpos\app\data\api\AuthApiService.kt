package com.rcpos.app.data.api

import com.rcpos.app.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface AuthApiService {
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<ApiResponse<AuthResponse>>
    
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<ApiResponse<User>>
    
    @POST("auth/change-password")
    suspend fun changePassword(@Body request: ChangePasswordRequest): Response<ApiResponse<Unit>>
    
    @POST("auth/refresh-token")
    suspend fun refreshToken(): Response<ApiResponse<AuthResponse>>
    
    @GET("auth/profile")
    suspend fun getProfile(): Response<ApiResponse<User>>
}
