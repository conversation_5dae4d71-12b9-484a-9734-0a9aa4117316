package com.rcpos.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "customers")
data class Customer(
    @PrimaryKey
    val id: String,
    @SerializedName("firstName")
    val firstName: String?,
    @SerializedName("lastName")
    val lastName: String?,
    val email: String?,
    val phone: String?,
    @SerializedName("dateOfBirth")
    val dateOfBirth: String?,
    @SerializedName("isActive")
    val isActive: Boolean,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable {
    val fullName: String
        get() = when {
            firstName != null && lastName != null -> "$firstName $lastName"
            firstName != null -> firstName
            lastName != null -> lastName
            else -> "Guest Customer"
        }
}

@Parcelize
data class CreateCustomerRequest(
    val firstName: String?,
    val lastName: String?,
    val email: String?,
    val phone: String?,
    val dateOfBirth: String?
) : Parcelable

@Parcelize
data class UpdateCustomerRequest(
    val firstName: String?,
    val lastName: String?,
    val email: String?,
    val phone: String?,
    val dateOfBirth: String?,
    val isActive: Boolean?
) : Parcelable
