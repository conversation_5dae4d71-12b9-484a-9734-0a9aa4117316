import React, { useEffect } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  useTheme,
  IconButton,
  Divider 
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { RootState, AppDispatch } from '@/store';
import { logout } from '@/store/slices/authSlice';
import { fetchProducts } from '@/store/slices/productSlice';
import { setRefreshing } from '@/store/slices/uiSlice';
import { COLORS, DEFAULTS } from '@/constants';

const DashboardScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  
  const { data: user } = useSelector((state: RootState) => state.auth);
  const { items: cartItems, total: cartTotal, itemCount } = useSelector((state: RootState) => state.cart);
  const { data: products } = useSelector((state: RootState) => state.products);
  const { isRefreshing } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    // Load initial data
    dispatch(fetchProducts({ limit: 10 }));
  }, [dispatch]);

  const handleRefresh = async () => {
    dispatch(setRefreshing(true));
    await dispatch(fetchProducts({ limit: 10 }));
    dispatch(setRefreshing(false));
  };

  const handleLogout = () => {
    dispatch(logout());
  };

  const stats = [
    {
      title: 'Products',
      value: products?.length || 0,
      icon: 'package-variant',
      color: COLORS.primary,
    },
    {
      title: 'Cart Items',
      value: itemCount,
      icon: 'cart',
      color: COLORS.accent,
    },
    {
      title: 'Cart Total',
      value: `${DEFAULTS.CURRENCY}${cartTotal.toFixed(2)}`,
      icon: 'currency-php',
      color: COLORS.warning,
    },
    {
      title: 'Orders Today',
      value: 0, // TODO: Implement actual order count
      icon: 'receipt',
      color: COLORS.info,
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Header */}
        <Card style={styles.headerCard}>
          <Card.Content style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Text variant="headlineSmall" style={styles.welcomeText}>
                Welcome back!
              </Text>
              <Text variant="bodyLarge" style={[styles.userText, { color: theme.colors.onSurfaceVariant }]}>
                {user?.firstName} {user?.lastName}
              </Text>
              <Text variant="bodySmall" style={[styles.roleText, { color: theme.colors.primary }]}>
                {user?.role}
              </Text>
            </View>
            <IconButton
              icon="logout"
              size={24}
              onPress={handleLogout}
              style={styles.logoutButton}
            />
          </Card.Content>
        </Card>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          {stats.map((stat, index) => (
            <Card key={index} style={styles.statCard}>
              <Card.Content style={styles.statContent}>
                <View style={styles.statHeader}>
                  <MaterialCommunityIcons
                    name={stat.icon as any}
                    size={24}
                    color={stat.color}
                  />
                  <Text variant="bodySmall" style={[styles.statTitle, { color: theme.colors.onSurfaceVariant }]}>
                    {stat.title}
                  </Text>
                </View>
                <Text variant="headlineSmall" style={[styles.statValue, { color: stat.color }]}>
                  {stat.value}
                </Text>
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* Quick Actions */}
        <Card style={styles.actionsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Quick Actions
            </Text>
            <Divider style={styles.divider} />
            
            <View style={styles.actionsGrid}>
              <Button
                mode="contained-tonal"
                icon="package-variant-plus"
                style={styles.actionButton}
                contentStyle={styles.actionButtonContent}
                onPress={() => {/* Navigate to add product */}}
              >
                Add Product
              </Button>
              
              <Button
                mode="contained-tonal"
                icon="cart-plus"
                style={styles.actionButton}
                contentStyle={styles.actionButtonContent}
                onPress={() => {/* Navigate to products */}}
              >
                Browse Products
              </Button>
              
              <Button
                mode="contained-tonal"
                icon="receipt-text"
                style={styles.actionButton}
                contentStyle={styles.actionButtonContent}
                onPress={() => {/* Navigate to orders */}}
              >
                View Orders
              </Button>
              
              <Button
                mode="contained-tonal"
                icon="barcode-scan"
                style={styles.actionButton}
                contentStyle={styles.actionButtonContent}
                onPress={() => {/* Open barcode scanner */}}
              >
                Scan Barcode
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Recent Activity */}
        <Card style={styles.activityCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Recent Activity
            </Text>
            <Divider style={styles.divider} />
            
            <View style={styles.activityList}>
              <Text variant="bodyMedium" style={[styles.noActivity, { color: theme.colors.onSurfaceVariant }]}>
                No recent activity
              </Text>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  headerCard: {
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  welcomeText: {
    fontWeight: 'bold',
  },
  userText: {
    marginTop: 4,
  },
  roleText: {
    marginTop: 2,
    fontWeight: '500',
  },
  logoutButton: {
    margin: 0,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statHeader: {
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    marginTop: 4,
    textAlign: 'center',
  },
  statValue: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  actionsCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  divider: {
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    minWidth: '45%',
  },
  actionButtonContent: {
    height: 48,
  },
  activityCard: {
    marginBottom: 16,
  },
  activityList: {
    minHeight: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noActivity: {
    fontStyle: 'italic',
  },
});

export default DashboardScreen;
