import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UiState {
  isOnline: boolean;
  theme: 'light' | 'dark' | 'auto';
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: number;
  }>;
  activeScreen: string;
  isRefreshing: boolean;
}

const initialState: UiState = {
  isOnline: true,
  theme: 'light',
  notifications: [],
  activeScreen: 'Dashboard',
  isRefreshing: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.theme = action.payload;
    },
    addNotification: (state, action: PayloadAction<{
      type: 'success' | 'error' | 'warning' | 'info';
      message: string;
    }>) => {
      const notification = {
        id: Date.now().toString(),
        ...action.payload,
        timestamp: Date.now(),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    setActiveScreen: (state, action: PayloadAction<string>) => {
      state.activeScreen = action.payload;
    },
    setRefreshing: (state, action: PayloadAction<boolean>) => {
      state.isRefreshing = action.payload;
    },
  },
});

export const {
  setOnlineStatus,
  setTheme,
  addNotification,
  removeNotification,
  clearNotifications,
  setActiveScreen,
  setRefreshing,
} = uiSlice.actions;

export default uiSlice.reducer;
