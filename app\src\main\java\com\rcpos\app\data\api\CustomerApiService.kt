package com.rcpos.app.data.api

import com.rcpos.app.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface CustomerApiService {
    
    @GET("customers")
    suspend fun getCustomers(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20,
        @Query("search") search: String? = null,
        @Query("isActive") isActive: Boolean? = null
    ): Response<ApiResponse<PaginatedResponse<Customer>>>
    
    @GET("customers/{id}")
    suspend fun getCustomerById(@Path("id") id: String): Response<ApiResponse<Customer>>
    
    @POST("customers")
    suspend fun createCustomer(@Body request: CreateCustomerRequest): Response<ApiResponse<Customer>>
    
    @PUT("customers/{id}")
    suspend fun updateCustomer(
        @Path("id") id: String,
        @Body request: UpdateCustomerRequest
    ): Response<ApiResponse<Customer>>
    
    @DELETE("customers/{id}")
    suspend fun deleteCustomer(@Path("id") id: String): Response<ApiResponse<Unit>>
}
