import Constants from 'expo-constants';

// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ 
    ? 'http://localhost:3000/api' 
    : 'https://your-production-api.com/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
};

// App Configuration
export const APP_CONFIG = {
  NAME: 'RC POS',
  VERSION: Constants.expoConfig?.version || '1.0.0',
  BUILD_NUMBER: Constants.expoConfig?.ios?.buildNumber || '1',
};

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  CART_DATA: 'cart_data',
  SETTINGS: 'app_settings',
};

// Default Values
export const DEFAULTS = {
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  TAX_RATE: 0.12, // 12% VAT
  CURRENCY: '₱',
  CURRENCY_CODE: 'PHP',
};

// Colors
export const COLORS = {
  primary: '#1976D2',
  primaryDark: '#1565C0',
  secondary: '#42A5F5',
  accent: '#4CAF50',
  error: '#E53935',
  warning: '#FF9800',
  success: '#4CAF50',
  info: '#2196F3',
  background: '#F5F5F5',
  surface: '#FFFFFF',
  text: '#212121',
  textSecondary: '#757575',
  border: '#E0E0E0',
};

// Dimensions
export const DIMENSIONS = {
  HEADER_HEIGHT: 56,
  TAB_BAR_HEIGHT: 60,
  BUTTON_HEIGHT: 48,
  INPUT_HEIGHT: 56,
  CARD_BORDER_RADIUS: 8,
  BUTTON_BORDER_RADIUS: 4,
};

// Animation Durations
export const ANIMATIONS = {
  SHORT: 200,
  MEDIUM: 300,
  LONG: 500,
};

// Validation Rules
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^[+]?[\d\s\-\(\)]+$/,
  PASSWORD_MIN_LENGTH: 6,
  USERNAME_MIN_LENGTH: 3,
  NAME_MIN_LENGTH: 1,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'Session expired. Please login again.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  REGISTER_SUCCESS: 'Registration successful',
  UPDATE_SUCCESS: 'Updated successfully',
  DELETE_SUCCESS: 'Deleted successfully',
  CREATE_SUCCESS: 'Created successfully',
  SAVE_SUCCESS: 'Saved successfully',
};

// Feature Flags
export const FEATURES = {
  BARCODE_SCANNING: true,
  OFFLINE_MODE: true,
  RECEIPT_PRINTING: true,
  CUSTOMER_MANAGEMENT: true,
  INVENTORY_TRACKING: true,
  ANALYTICS: true,
  MULTI_PAYMENT: true,
};

// Database Configuration
export const DATABASE = {
  NAME: 'rcpos.db',
  VERSION: 1,
  TABLES: {
    USERS: 'users',
    PRODUCTS: 'products',
    CATEGORIES: 'categories',
    CUSTOMERS: 'customers',
    ORDERS: 'orders',
    ORDER_ITEMS: 'order_items',
    PAYMENTS: 'payments',
  },
};
