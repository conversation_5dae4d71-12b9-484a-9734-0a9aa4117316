{"name": "rcpos-expo", "version": "1.0.0", "description": "React Native Expo POS Application", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "expo": "~49.0.15", "expo-barcode-scanner": "~12.5.3", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-file-system": "~15.4.5", "expo-font": "~11.4.0", "expo-linking": "~5.0.2", "expo-print": "~12.4.2", "expo-router": "^2.0.0", "expo-secure-store": "~12.3.1", "expo-sharing": "~11.5.0", "expo-splash-screen": "~0.20.5", "expo-sqlite": "~11.3.3", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-paper": "^5.11.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.0.3", "react-redux": "^9.0.4", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.0.0", "eslint-config-expo": "^7.0.0", "jest": "^29.2.1", "typescript": "^5.1.3"}, "keywords": ["pos", "point-of-sale", "react-native", "expo", "typescript"], "author": "RC POS Team", "license": "MIT", "private": true}