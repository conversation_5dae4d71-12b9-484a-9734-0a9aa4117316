# RC POS - React Native Expo Application

A comprehensive Point of Sale (POS) system built with React Native and Expo, designed for F&B and confectionery businesses.

## Features

- **Authentication System**: Secure login/logout with JWT tokens
- **Product Management**: Browse, search, and manage product catalog
- **Shopping Cart**: Add/remove items, quantity management, notes
- **Order Processing**: Complete checkout workflow with payment handling
- **Inventory Management**: Real-time stock tracking and low stock alerts
- **Offline Support**: Local data caching with synchronization
- **Barcode Scanning**: Product lookup via barcode scanning
- **Receipt Generation**: Digital receipt creation and sharing
- **Role-based Access**: Different permissions for Admin, Manager, and Cashier
- **Material Design 3**: Modern, responsive UI components

## Technology Stack

- **React Native** with **Expo SDK 49**
- **TypeScript** for type safety
- **Redux Toolkit** for state management
- **React Navigation 6** for navigation
- **React Native Paper** for UI components
- **Axios** for API communication
- **Expo SQLite** for local database
- **Expo Secure Store** for secure token storage
- **Expo Barcode Scanner** for barcode functionality

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (for iOS development) or Android Studio (for Android development)
- Expo Go app on your mobile device (for testing)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd rcpos-expo
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Configure environment**
   - Update the `API_CONFIG.BASE_URL` in `src/constants/index.ts` to point to your backend server
   - For development, ensure your backend is running on `http://localhost:3000`

4. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

5. **Run on device/simulator**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app for physical device testing

## Backend Integration

This app is designed to work with the TypeScript POS backend located at `../pos-backend`. Ensure the backend is running before using the app.

### API Endpoints Used

- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/refresh-token` - Refresh JWT token
- `GET /api/products` - Get products with pagination and filters
- `GET /api/products/:id` - Get single product
- `POST /api/orders` - Create new order
- `GET /api/orders` - Get orders with filters
- `GET /api/categories` - Get product categories
- `GET /api/customers` - Get customers

### Demo Credentials

The app includes demo credentials for testing:

- **Admin**: <EMAIL> / admin123
- **Manager**: <EMAIL> / manager123
- **Cashier**: <EMAIL> / cashier123

## Project Structure

```
src/
├── components/          # Reusable UI components
├── constants/          # App constants and configuration
├── navigation/         # Navigation configuration
├── screens/           # Screen components
│   ├── auth/         # Authentication screens
│   └── main/         # Main app screens
├── services/         # API services
├── store/           # Redux store and slices
├── theme/           # Theme configuration
├── types/           # TypeScript type definitions
└── utils/           # Utility functions
```

## Building for Production

### Development Build
```bash
npm run build:android
npm run build:ios
```

### Production Build
```bash
eas build --platform android --profile production
eas build --platform ios --profile production
```

### Submit to App Stores
```bash
npm run submit:android
npm run submit:ios
```

## Key Features Implementation

### State Management
- Redux Toolkit for global state management
- Redux Persist for data persistence
- Separate slices for auth, products, cart, orders, etc.

### Offline Support
- Local SQLite database for caching
- Automatic sync when connection is restored
- Optimistic updates for better UX

### Security
- JWT token authentication
- Secure storage for sensitive data
- API request/response interceptors
- Role-based access control

### UI/UX
- Material Design 3 components
- Responsive design for different screen sizes
- Loading states and error handling
- Pull-to-refresh functionality
- Search and filtering capabilities

## Development Guidelines

### Code Style
- Use TypeScript for all new files
- Follow React Native best practices
- Use functional components with hooks
- Implement proper error boundaries

### State Management
- Use Redux Toolkit for global state
- Keep local state for component-specific data
- Use async thunks for API calls
- Implement proper loading and error states

### API Integration
- Use the centralized API client
- Implement proper error handling
- Add request/response interceptors
- Handle authentication token refresh

## Troubleshooting

### Common Issues

1. **Metro bundler issues**
   ```bash
   npx expo start --clear
   ```

2. **iOS build issues**
   ```bash
   cd ios && pod install
   ```

3. **Android build issues**
   ```bash
   cd android && ./gradlew clean
   ```

4. **Network issues**
   - Ensure backend server is running
   - Check API_CONFIG.BASE_URL in constants
   - Verify network connectivity

### Debug Mode
- Enable debug mode in Expo Dev Tools
- Use React Native Debugger for advanced debugging
- Check console logs for API errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the development team or create an issue in the repository.
