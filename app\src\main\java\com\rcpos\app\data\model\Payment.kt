package com.rcpos.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "payments")
data class Payment(
    @PrimaryKey
    val id: String,
    val amount: Double,
    val method: PaymentMethod,
    val status: PaymentStatus,
    val reference: String?,
    val notes: String?,
    @SerializedName("orderId")
    val orderId: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable {
    val formattedAmount: String
        get() = "₱%.2f".format(amount)
}

enum class PaymentMethod {
    @SerializedName("CASH")
    CASH,
    @SerializedName("CARD")
    CARD,
    @SerializedName("DIGITAL_WALLET")
    DIGITAL_WALLET,
    @SerializedName("BANK_TRANSFER")
    BANK_TRANSFER
}

enum class PaymentStatus {
    @SerializedName("PENDING")
    PENDING,
    @SerializedName("COMPLETED")
    COMPLETED,
    @SerializedName("FAILED")
    FAILED,
    @SerializedName("REFUNDED")
    REFUNDED
}

@Parcelize
data class CreatePaymentRequest(
    val amount: Double,
    val method: PaymentMethod,
    val reference: String? = null,
    val notes: String? = null
) : Parcelable

@Parcelize
data class UpdatePaymentRequest(
    val status: PaymentStatus,
    val reference: String? = null,
    val notes: String? = null
) : Parcelable
