import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, UiState, PaginatedResponse, ProductType } from '@/types';
import { productService } from '@/services/productService';

interface ProductState extends UiState<Product[]> {
  selectedProduct?: Product;
  searchResults: Product[];
  lowStockProducts: Product[];
  categories: { [categoryId: string]: Product[] };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

const initialState: ProductState = {
  isLoading: false,
  data: [],
  searchResults: [],
  lowStockProducts: [],
  categories: {},
  error: undefined,
};

// Async thunks
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (params: {
    page?: number;
    limit?: number;
    search?: string;
    categoryId?: string;
    type?: ProductType;
    isActive?: boolean;
    lowStock?: boolean;
  } = {}, { rejectWithValue }) => {
    try {
      const response = await productService.getProducts(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch products');
    }
  }
);

export const fetchProductById = createAsyncThunk(
  'products/fetchProductById',
  async (id: string, { rejectWithValue }) => {
    try {
      const product = await productService.getProductById(id);
      return product;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch product');
    }
  }
);

export const searchProducts = createAsyncThunk(
  'products/searchProducts',
  async (query: string, { rejectWithValue }) => {
    try {
      const products = await productService.searchProducts(query);
      return products;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to search products');
    }
  }
);

export const fetchLowStockProducts = createAsyncThunk(
  'products/fetchLowStockProducts',
  async (_, { rejectWithValue }) => {
    try {
      const products = await productService.getLowStockProducts();
      return products;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch low stock products');
    }
  }
);

export const fetchProductsByCategory = createAsyncThunk(
  'products/fetchProductsByCategory',
  async (categoryId: string, { rejectWithValue }) => {
    try {
      const products = await productService.getProductsByCategory(categoryId);
      return { categoryId, products };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch products by category');
    }
  }
);

const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = undefined;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    setSelectedProduct: (state, action: PayloadAction<Product | undefined>) => {
      state.selectedProduct = action.payload;
    },
    updateProductInList: (state, action: PayloadAction<Product>) => {
      const updatedProduct = action.payload;
      
      // Update in main products list
      if (state.data) {
        const index = state.data.findIndex(p => p.id === updatedProduct.id);
        if (index !== -1) {
          state.data[index] = updatedProduct;
        }
      }
      
      // Update in search results
      const searchIndex = state.searchResults.findIndex(p => p.id === updatedProduct.id);
      if (searchIndex !== -1) {
        state.searchResults[searchIndex] = updatedProduct;
      }
      
      // Update in category lists
      Object.keys(state.categories).forEach(categoryId => {
        const categoryIndex = state.categories[categoryId].findIndex(p => p.id === updatedProduct.id);
        if (categoryIndex !== -1) {
          state.categories[categoryId][categoryIndex] = updatedProduct;
        }
      });
      
      // Update selected product if it matches
      if (state.selectedProduct?.id === updatedProduct.id) {
        state.selectedProduct = updatedProduct;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Products
      .addCase(fetchProducts.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data = action.payload.items;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Product By ID
      .addCase(fetchProductById.fulfilled, (state, action) => {
        state.selectedProduct = action.payload;
      })
      
      // Search Products
      .addCase(searchProducts.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(searchProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Low Stock Products
      .addCase(fetchLowStockProducts.fulfilled, (state, action) => {
        state.lowStockProducts = action.payload;
      })
      
      // Fetch Products By Category
      .addCase(fetchProductsByCategory.fulfilled, (state, action) => {
        const { categoryId, products } = action.payload;
        state.categories[categoryId] = products;
      });
  },
});

export const { 
  clearError, 
  clearSearchResults, 
  setSelectedProduct, 
  updateProductInList 
} = productSlice.actions;

export default productSlice.reducer;
