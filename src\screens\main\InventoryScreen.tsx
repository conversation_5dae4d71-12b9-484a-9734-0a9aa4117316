import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl } from 'react-native';
import { 
  Text, 
  Card, 
  Chip,
  useTheme,
  ActivityIndicator,
  SegmentedButtons,
  ProgressBar
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';

import { RootState, AppDispatch } from '@/store';
import { fetchProducts, fetchLowStockProducts } from '@/store/slices/productSlice';
import { Product } from '@/types';
import { DEFAULTS } from '@/constants';

const InventoryScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  
  const { data: products, lowStockProducts, isLoading } = useSelector((state: RootState) => state.products);
  const [selectedView, setSelectedView] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);

  const viewOptions = [
    { value: 'all', label: 'All Items' },
    { value: 'low-stock', label: 'Low Stock' },
    { value: 'out-of-stock', label: 'Out of Stock' },
  ];

  useEffect(() => {
    dispatch(fetchProducts({ limit: 100 }));
    dispatch(fetchLowStockProducts());
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      dispatch(fetchProducts({ limit: 100 })),
      dispatch(fetchLowStockProducts())
    ]);
    setRefreshing(false);
  };

  const getDisplayProducts = () => {
    const allProducts = products || [];
    
    switch (selectedView) {
      case 'low-stock':
        return lowStockProducts;
      case 'out-of-stock':
        return allProducts.filter(product => product.stockQuantity === 0);
      default:
        return allProducts;
    }
  };

  const getStockStatus = (product: Product) => {
    if (product.stockQuantity === 0) {
      return { label: 'Out of Stock', color: theme.colors.error };
    } else if (product.stockQuantity <= product.minStockLevel) {
      return { label: 'Low Stock', color: '#FF9800' };
    } else {
      return { label: 'In Stock', color: '#4CAF50' };
    }
  };

  const getStockPercentage = (product: Product) => {
    if (!product.trackInventory) return 1;
    const maxStock = Math.max(product.stockQuantity, product.minStockLevel * 2);
    return product.stockQuantity / maxStock;
  };

  const renderProduct = ({ item }: { item: Product }) => {
    const stockStatus = getStockStatus(item);
    const stockPercentage = getStockPercentage(item);

    return (
      <Card style={styles.productCard}>
        <Card.Content>
          <View style={styles.productHeader}>
            <Text variant="titleMedium" style={styles.productName}>
              {item.name}
            </Text>
            <Chip 
              mode="flat"
              style={[styles.statusChip, { backgroundColor: stockStatus.color + '20' }]}
              textStyle={{ color: stockStatus.color }}
            >
              {stockStatus.label}
            </Chip>
          </View>
          
          <Text variant="bodySmall" style={[styles.productSku, { color: theme.colors.onSurfaceVariant }]}>
            SKU: {item.sku || 'N/A'} | Type: {item.type}
          </Text>
          
          <View style={styles.stockInfo}>
            <View style={styles.stockNumbers}>
              <Text variant="bodyMedium">
                Current Stock: <Text style={styles.stockValue}>{item.stockQuantity}</Text>
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Min Level: {item.minStockLevel}
              </Text>
            </View>
            
            <Text variant="titleMedium" style={[styles.price, { color: theme.colors.primary }]}>
              {DEFAULTS.CURRENCY}{item.price.toFixed(2)}
            </Text>
          </View>
          
          {item.trackInventory && (
            <View style={styles.progressContainer}>
              <ProgressBar 
                progress={stockPercentage}
                color={stockStatus.color}
                style={styles.progressBar}
              />
              <Text variant="bodySmall" style={[styles.progressText, { color: theme.colors.onSurfaceVariant }]}>
                {Math.round(stockPercentage * 100)}% of recommended level
              </Text>
            </View>
          )}
          
          {!item.trackInventory && (
            <Text variant="bodySmall" style={[styles.noTracking, { color: theme.colors.onSurfaceVariant }]}>
              Inventory tracking disabled
            </Text>
          )}
        </Card.Content>
      </Card>
    );
  };

  const displayProducts = getDisplayProducts();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <SegmentedButtons
          value={selectedView}
          onValueChange={setSelectedView}
          buttons={viewOptions}
          style={styles.segmentedButtons}
        />
        
        {isLoading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text variant="bodyMedium" style={styles.loadingText}>
              Loading inventory...
            </Text>
          </View>
        ) : (
          <FlatList
            data={displayProducts}
            renderItem={renderProduct}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[theme.colors.primary]}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text variant="bodyLarge" style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
                  {selectedView === 'all' ? 'No products found' : `No ${selectedView.replace('-', ' ')} items`}
                </Text>
              </View>
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  listContent: {
    paddingBottom: 16,
  },
  productCard: {
    marginBottom: 12,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  productName: {
    flex: 1,
    fontWeight: 'bold',
    marginRight: 8,
  },
  statusChip: {
    height: 28,
  },
  productSku: {
    marginBottom: 12,
  },
  stockInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  stockNumbers: {
    flex: 1,
  },
  stockValue: {
    fontWeight: 'bold',
  },
  price: {
    fontWeight: 'bold',
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: 4,
  },
  progressText: {
    textAlign: 'center',
  },
  noTracking: {
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    textAlign: 'center',
  },
});

export default InventoryScreen;
