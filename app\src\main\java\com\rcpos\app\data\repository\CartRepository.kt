package com.rcpos.app.data.repository

import com.rcpos.app.data.model.CartItem
import com.rcpos.app.data.model.Product
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CartRepository @Inject constructor() {
    
    private val _cartItems = MutableStateFlow<List<CartItem>>(emptyList())
    val cartItems: StateFlow<List<CartItem>> = _cartItems.asStateFlow()
    
    private val _cartTotal = MutableStateFlow(0.0)
    val cartTotal: StateFlow<Double> = _cartTotal.asStateFlow()
    
    private val _cartItemCount = MutableStateFlow(0)
    val cartItemCount: StateFlow<Int> = _cartItemCount.asStateFlow()
    
    fun addToCart(product: Product, quantity: Int = 1, notes: String? = null) {
        val currentItems = _cartItems.value.toMutableList()
        val existingItemIndex = currentItems.indexOfFirst { it.product.id == product.id }
        
        if (existingItemIndex != -1) {
            // Update existing item
            val existingItem = currentItems[existingItemIndex]
            currentItems[existingItemIndex] = existingItem.copy(
                quantity = existingItem.quantity + quantity,
                notes = notes ?: existingItem.notes
            )
        } else {
            // Add new item
            currentItems.add(CartItem(product, quantity, notes))
        }
        
        updateCart(currentItems)
    }
    
    fun removeFromCart(productId: String) {
        val currentItems = _cartItems.value.toMutableList()
        currentItems.removeAll { it.product.id == productId }
        updateCart(currentItems)
    }
    
    fun updateQuantity(productId: String, quantity: Int) {
        if (quantity <= 0) {
            removeFromCart(productId)
            return
        }
        
        val currentItems = _cartItems.value.toMutableList()
        val itemIndex = currentItems.indexOfFirst { it.product.id == productId }
        
        if (itemIndex != -1) {
            currentItems[itemIndex] = currentItems[itemIndex].copy(quantity = quantity)
            updateCart(currentItems)
        }
    }
    
    fun updateNotes(productId: String, notes: String?) {
        val currentItems = _cartItems.value.toMutableList()
        val itemIndex = currentItems.indexOfFirst { it.product.id == productId }
        
        if (itemIndex != -1) {
            currentItems[itemIndex] = currentItems[itemIndex].copy(notes = notes)
            updateCart(currentItems)
        }
    }
    
    fun clearCart() {
        updateCart(emptyList())
    }
    
    fun getCartItem(productId: String): CartItem? {
        return _cartItems.value.find { it.product.id == productId }
    }
    
    fun isInCart(productId: String): Boolean {
        return _cartItems.value.any { it.product.id == productId }
    }
    
    private fun updateCart(items: List<CartItem>) {
        _cartItems.value = items
        _cartTotal.value = items.sumOf { it.totalPrice }
        _cartItemCount.value = items.sumOf { it.quantity }
    }
    
    fun calculateSubtotal(): Double = _cartTotal.value
    
    fun calculateTax(taxRate: Double = 0.12): Double = _cartTotal.value * taxRate
    
    fun calculateTotal(taxRate: Double = 0.12, discountAmount: Double = 0.0): Double {
        val subtotal = calculateSubtotal()
        val tax = calculateTax(taxRate)
        return subtotal + tax - discountAmount
    }
}
