package com.rcpos.app.data.network

import com.rcpos.app.data.local.preferences.AuthPreferences
import com.rcpos.app.data.model.ApiConstants
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthInterceptor @Inject constructor(
    private val authPreferences: AuthPreferences
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // Skip auth for login and register endpoints
        val url = originalRequest.url.toString()
        if (url.contains("/auth/login") || url.contains("/auth/register")) {
            return chain.proceed(originalRequest)
        }
        
        // Get token from preferences
        val token = runBlocking {
            authPreferences.getToken().first()
        }
        
        // Add authorization header if token exists
        val newRequest = if (token != null) {
            originalRequest.newBuilder()
                .addHeader(ApiConstants.HEADER_AUTHORIZATION, "${ApiConstants.BEARER_PREFIX}$token")
                .addHeader(ApiConstants.HEADER_CONTENT_TYPE, ApiConstants.CONTENT_TYPE_JSON)
                .addHeader(ApiConstants.HEADER_ACCEPT, ApiConstants.CONTENT_TYPE_JSON)
                .build()
        } else {
            originalRequest.newBuilder()
                .addHeader(ApiConstants.HEADER_CONTENT_TYPE, ApiConstants.CONTENT_TYPE_JSON)
                .addHeader(ApiConstants.HEADER_ACCEPT, ApiConstants.CONTENT_TYPE_JSON)
                .build()
        }
        
        return chain.proceed(newRequest)
    }
}
