package com.rcpos.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "products")
data class Product(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String?,
    val price: Double,
    val cost: Double?,
    val sku: String?,
    val barcode: String?,
    val type: ProductType,
    @SerializedName("isActive")
    val isActive: Boolean,
    @SerializedName("stockQuantity")
    val stockQuantity: Int,
    @SerializedName("minStockLevel")
    val minStockLevel: Int,
    @SerializedName("trackInventory")
    val trackInventory: Boolean,
    @SerializedName("preparationTime")
    val preparationTime: Int?,
    val calories: Int?,
    val allergens: String?,
    @SerializedName("categoryId")
    val categoryId: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable {
    val isLowStock: Boolean
        get() = trackInventory && stockQuantity <= minStockLevel
    
    val formattedPrice: String
        get() = "₱%.2f".format(price)
}

enum class ProductType {
    @SerializedName("FOOD")
    FOOD,
    @SerializedName("BEVERAGE")
    BEVERAGE,
    @SerializedName("CONFECTIONERY")
    CONFECTIONERY,
    @SerializedName("COMBO")
    COMBO
}

@Parcelize
data class CreateProductRequest(
    val name: String,
    val description: String?,
    val price: Double,
    val cost: Double?,
    val sku: String?,
    val barcode: String?,
    val type: ProductType,
    val categoryId: String,
    val stockQuantity: Int = 0,
    val minStockLevel: Int = 0,
    val trackInventory: Boolean = true,
    val preparationTime: Int?,
    val calories: Int?,
    val allergens: String?
) : Parcelable

@Parcelize
data class UpdateProductRequest(
    val name: String?,
    val description: String?,
    val price: Double?,
    val cost: Double?,
    val sku: String?,
    val barcode: String?,
    val type: ProductType?,
    val categoryId: String?,
    val stockQuantity: Int?,
    val minStockLevel: Int?,
    val trackInventory: Boolean?,
    val preparationTime: Int?,
    val calories: Int?,
    val allergens: String?,
    val isActive: Boolean?
) : Parcelable

@Parcelize
data class UpdateStockRequest(
    val quantity: Int,
    val type: StockUpdateType,
    val reason: String?
) : Parcelable

enum class StockUpdateType {
    @SerializedName("ADD")
    ADD,
    @SerializedName("SUBTRACT")
    SUBTRACT,
    @SerializedName("SET")
    SET
}

@Parcelize
data class ProductQuery(
    val page: Int = 1,
    val limit: Int = 10,
    val search: String? = null,
    val categoryId: String? = null,
    val type: ProductType? = null,
    val isActive: Boolean? = null,
    val lowStock: Boolean? = null
) : Parcelable
