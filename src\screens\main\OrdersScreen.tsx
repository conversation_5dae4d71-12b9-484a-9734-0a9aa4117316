import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl } from 'react-native';
import { 
  Text, 
  Card, 
  Chip,
  useTheme,
  ActivityIndicator,
  SegmentedButtons
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';

import { RootState, AppDispatch } from '@/store';
import { fetchOrders } from '@/store/slices/orderSlice';
import { Order, OrderStatus } from '@/types';
import { DEFAULTS } from '@/constants';

const OrdersScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  
  const { data: orders, isLoading } = useSelector((state: RootState) => state.orders);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);

  const statusOptions = [
    { value: 'all', label: 'All' },
    { value: OrderStatus.PENDING, label: 'Pending' },
    { value: OrderStatus.PREPARING, label: 'Preparing' },
    { value: OrderStatus.READY, label: 'Ready' },
    { value: OrderStatus.COMPLETED, label: 'Completed' },
  ];

  useEffect(() => {
    dispatch(fetchOrders({}));
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchOrders({}));
    setRefreshing(false);
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return theme.colors.outline;
      case OrderStatus.PREPARING:
        return '#FF9800';
      case OrderStatus.READY:
        return '#2196F3';
      case OrderStatus.COMPLETED:
        return '#4CAF50';
      case OrderStatus.CANCELLED:
        return theme.colors.error;
      default:
        return theme.colors.outline;
    }
  };

  const filteredOrders = selectedStatus === 'all' 
    ? orders || []
    : (orders || []).filter(order => order.status === selectedStatus);

  const renderOrder = ({ item }: { item: Order }) => (
    <Card style={styles.orderCard}>
      <Card.Content>
        <View style={styles.orderHeader}>
          <Text variant="titleMedium" style={styles.orderNumber}>
            #{item.orderNumber}
          </Text>
          <Chip 
            mode="flat"
            style={[styles.statusChip, { backgroundColor: getStatusColor(item.status) + '20' }]}
            textStyle={{ color: getStatusColor(item.status) }}
          >
            {item.status}
          </Chip>
        </View>
        
        <Text variant="bodySmall" style={[styles.orderDate, { color: theme.colors.onSurfaceVariant }]}>
          {new Date(item.orderDate).toLocaleDateString()} at {new Date(item.orderDate).toLocaleTimeString()}
        </Text>
        
        <View style={styles.orderDetails}>
          <Text variant="bodyMedium">
            Customer: {item.customerId ? 'Registered Customer' : 'Walk-in'}
          </Text>
          <Text variant="titleMedium" style={[styles.orderTotal, { color: theme.colors.primary }]}>
            {DEFAULTS.CURRENCY}{item.totalAmount.toFixed(2)}
          </Text>
        </View>
        
        {item.completedAt && (
          <Text variant="bodySmall" style={[styles.completedAt, { color: theme.colors.onSurfaceVariant }]}>
            Completed: {new Date(item.completedAt).toLocaleDateString()} at {new Date(item.completedAt).toLocaleTimeString()}
          </Text>
        )}
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <SegmentedButtons
          value={selectedStatus}
          onValueChange={setSelectedStatus}
          buttons={statusOptions}
          style={styles.segmentedButtons}
        />
        
        {isLoading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text variant="bodyMedium" style={styles.loadingText}>
              Loading orders...
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredOrders}
            renderItem={renderOrder}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[theme.colors.primary]}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text variant="bodyLarge" style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
                  {selectedStatus === 'all' ? 'No orders found' : `No ${selectedStatus.toLowerCase()} orders`}
                </Text>
              </View>
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  listContent: {
    paddingBottom: 16,
  },
  orderCard: {
    marginBottom: 12,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontWeight: 'bold',
  },
  statusChip: {
    height: 28,
  },
  orderDate: {
    marginBottom: 12,
  },
  orderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderTotal: {
    fontWeight: 'bold',
  },
  completedAt: {
    fontStyle: 'italic',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    textAlign: 'center',
  },
});

export default OrdersScreen;
