package com.rcpos.app.di

import android.content.Context
import com.rcpos.app.data.local.PosDatabase
import com.rcpos.app.data.local.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): PosDatabase {
        return PosDatabase.getDatabase(context)
    }
    
    @Provides
    fun provideUserDao(database: PosDatabase): UserDao {
        return database.userDao()
    }
    
    @Provides
    fun provideProductDao(database: PosDatabase): ProductDao {
        return database.productDao()
    }
    
    @Provides
    fun provideCategoryDao(database: PosDatabase): CategoryDao {
        return database.categoryDao()
    }
    
    @Provides
    fun provideCustomerDao(database: PosDatabase): CustomerDao {
        return database.customerDao()
    }
    
    @Provides
    fun provideOrderDao(database: PosDatabase): OrderDao {
        return database.orderDao()
    }
    
    @Provides
    fun provideOrderItemDao(database: PosDatabase): OrderItemDao {
        return database.orderItemDao()
    }
    
    @Provides
    fun providePaymentDao(database: PosDatabase): PaymentDao {
        return database.paymentDao()
    }
}
