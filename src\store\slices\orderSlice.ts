import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Order, UiState, OrderStatus } from '@/types';

interface OrderState extends UiState<Order[]> {
  selectedOrder?: Order;
  recentOrders: Order[];
  ordersByStatus: { [status: string]: Order[] };
}

const initialState: OrderState = {
  isLoading: false,
  data: [],
  recentOrders: [],
  ordersByStatus: {},
  error: undefined,
};

// Placeholder async thunks - implement with actual API calls
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async (params: { page?: number; limit?: number; status?: OrderStatus } = {}, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual API call
      return [];
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch orders');
    }
  }
);

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async (orderData: any, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual API call
      return {} as Order;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create order');
    }
  }
);

const orderSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = undefined;
    },
    setSelectedOrder: (state, action) => {
      state.selectedOrder = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data = action.payload;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setSelectedOrder } = orderSlice.actions;
export default orderSlice.reducer;
