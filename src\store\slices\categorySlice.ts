import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Category, UiState } from '@/types';

interface CategoryState extends UiState<Category[]> {
  selectedCategory?: Category;
}

const initialState: CategoryState = {
  isLoading: false,
  data: [],
  error: undefined,
};

// Placeholder async thunks
export const fetchCategories = createAsyncThunk(
  'categories/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual API call
      return [];
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

const categorySlice = createSlice({
  name: 'categories',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = undefined;
    },
    setSelectedCategory: (state, action) => {
      state.selectedCategory = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCategories.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setSelectedCategory } = categorySlice.actions;
export default categorySlice.reducer;
