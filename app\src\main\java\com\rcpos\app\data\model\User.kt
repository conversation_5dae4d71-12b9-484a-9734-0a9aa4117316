package com.rcpos.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "users")
data class User(
    @PrimaryKey
    val id: String,
    val email: String,
    val username: String,
    @SerializedName("firstName")
    val firstName: String,
    @SerializedName("lastName")
    val lastName: String,
    val role: UserRole,
    @SerializedName("isActive")
    val isActive: Boolean,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable {
    val fullName: String
        get() = "$firstName $lastName"
}

enum class UserRole {
    @SerializedName("ADMIN")
    ADMIN,
    @SerializedName("MANAGER")
    MANAGER,
    @SerializedName("CASHIER")
    CASHIER
}

@Parcelize
data class AuthResponse(
    val user: User,
    val token: String,
    @SerializedName("refreshToken")
    val refreshToken: String? = null
) : Parcelable

@Parcelize
data class LoginRequest(
    val email: String,
    val password: String
) : Parcelable

@Parcelize
data class RegisterRequest(
    val email: String,
    val username: String,
    val password: String,
    val firstName: String,
    val lastName: String,
    val role: UserRole = UserRole.CASHIER
) : Parcelable

@Parcelize
data class ChangePasswordRequest(
    val currentPassword: String,
    val newPassword: String,
    val confirmPassword: String
) : Parcelable
