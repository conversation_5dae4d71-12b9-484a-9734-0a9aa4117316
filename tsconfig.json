{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@types/*": ["src/types/*"], "@utils/*": ["src/utils/*"], "@constants/*": ["src/constants/*"]}, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": true}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}