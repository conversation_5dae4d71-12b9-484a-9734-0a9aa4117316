package com.rcpos.app.data.local.dao

import androidx.room.*
import com.rcpos.app.data.model.Payment
import com.rcpos.app.data.model.PaymentMethod
import com.rcpos.app.data.model.PaymentStatus
import kotlinx.coroutines.flow.Flow

@Dao
interface PaymentDao {
    
    @Query("SELECT * FROM payments ORDER BY createdAt DESC")
    fun getAllPayments(): Flow<List<Payment>>
    
    @Query("SELECT * FROM payments WHERE id = :id")
    suspend fun getPaymentById(id: String): Payment?
    
    @Query("SELECT * FROM payments WHERE orderId = :orderId")
    suspend fun getPaymentsByOrderId(orderId: String): List<Payment>
    
    @Query("SELECT * FROM payments WHERE orderId = :orderId")
    fun getPaymentsByOrderIdFlow(orderId: String): Flow<List<Payment>>
    
    @Query("SELECT * FROM payments WHERE method = :method ORDER BY createdAt DESC")
    fun getPaymentsByMethod(method: PaymentMethod): Flow<List<Payment>>
    
    @Query("SELECT * FROM payments WHERE status = :status ORDER BY createdAt DESC")
    fun getPaymentsByStatus(status: PaymentStatus): Flow<List<Payment>>
    
    @Query("SELECT * FROM payments WHERE reference = :reference")
    suspend fun getPaymentByReference(reference: String): Payment?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPayment(payment: Payment)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPayments(payments: List<Payment>)
    
    @Update
    suspend fun updatePayment(payment: Payment)
    
    @Delete
    suspend fun deletePayment(payment: Payment)
    
    @Query("DELETE FROM payments WHERE id = :id")
    suspend fun deletePaymentById(id: String)
    
    @Query("DELETE FROM payments WHERE orderId = :orderId")
    suspend fun deletePaymentsByOrderId(orderId: String)
    
    @Query("DELETE FROM payments")
    suspend fun deleteAllPayments()
    
    @Query("SELECT COUNT(*) FROM payments")
    suspend fun getPaymentCount(): Int
    
    @Query("SELECT COUNT(*) FROM payments WHERE status = :status")
    suspend fun getPaymentCountByStatus(status: PaymentStatus): Int
    
    @Query("SELECT SUM(amount) FROM payments WHERE status = 'COMPLETED'")
    suspend fun getTotalPayments(): Double?
    
    @Query("SELECT SUM(amount) FROM payments WHERE status = 'COMPLETED' AND method = :method")
    suspend fun getTotalPaymentsByMethod(method: PaymentMethod): Double?
}
