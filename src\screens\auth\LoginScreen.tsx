import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { 
  Text, 
  TextInput, 
  Button, 
  Card, 
  Snackbar,
  useTheme,
  ActivityIndicator 
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';

import { RootState, AppDispatch } from '@/store';
import { login, clearError } from '@/store/slices/authSlice';
import { APP_CONFIG, VALIDATION } from '@/constants';

const LoginScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  useEffect(() => {
    // Clear any previous errors when component mounts
    dispatch(clearError());
  }, [dispatch]);

  const validateForm = (): boolean => {
    let isValid = true;

    // Email validation
    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!VALIDATION.EMAIL_REGEX.test(email)) {
      setEmailError('Please enter a valid email');
      isValid = false;
    } else {
      setEmailError('');
    }

    // Password validation
    if (!password.trim()) {
      setPasswordError('Password is required');
      isValid = false;
    } else if (password.length < VALIDATION.PASSWORD_MIN_LENGTH) {
      setPasswordError(`Password must be at least ${VALIDATION.PASSWORD_MIN_LENGTH} characters`);
      isValid = false;
    } else {
      setPasswordError('');
    }

    return isValid;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    dispatch(login({ email: email.trim(), password }));
  };

  const fillDemoCredentials = (role: 'admin' | 'manager' | 'cashier') => {
    const credentials = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      manager: { email: '<EMAIL>', password: 'manager123' },
      cashier: { email: '<EMAIL>', password: 'cashier123' },
    };

    setEmail(credentials[role].email);
    setPassword(credentials[role].password);
    setEmailError('');
    setPasswordError('');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text variant="headlineLarge" style={[styles.title, { color: theme.colors.primary }]}>
              {APP_CONFIG.NAME}
            </Text>
            <Text variant="bodyLarge" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
              Point of Sale System
            </Text>
          </View>

          <Card style={styles.loginCard}>
            <Card.Content style={styles.cardContent}>
              <Text variant="headlineSmall" style={styles.loginTitle}>
                Sign In
              </Text>

              <TextInput
                label="Email"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                error={!!emailError}
                style={styles.input}
                left={<TextInput.Icon icon="email" />}
              />
              {emailError ? (
                <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                  {emailError}
                </Text>
              ) : null}

              <TextInput
                label="Password"
                value={password}
                onChangeText={setPassword}
                mode="outlined"
                secureTextEntry={!showPassword}
                autoComplete="password"
                error={!!passwordError}
                style={styles.input}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon 
                    icon={showPassword ? "eye-off" : "eye"} 
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
              />
              {passwordError ? (
                <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                  {passwordError}
                </Text>
              ) : null}

              <Button
                mode="contained"
                onPress={handleLogin}
                disabled={isLoading}
                style={styles.loginButton}
                contentStyle={styles.buttonContent}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color={theme.colors.onPrimary} />
                ) : (
                  'Sign In'
                )}
              </Button>
            </Card.Content>
          </Card>

          <Card style={styles.demoCard}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.demoTitle}>
                Demo Credentials
              </Text>
              <Text variant="bodySmall" style={[styles.demoSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                Tap to fill credentials
              </Text>

              <View style={styles.demoButtons}>
                <Button
                  mode="outlined"
                  onPress={() => fillDemoCredentials('admin')}
                  style={styles.demoButton}
                  compact
                >
                  Admin
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => fillDemoCredentials('manager')}
                  style={styles.demoButton}
                  compact
                >
                  Manager
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => fillDemoCredentials('cashier')}
                  style={styles.demoButton}
                  compact
                >
                  Cashier
                </Button>
              </View>
            </Card.Content>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      <Snackbar
        visible={!!error}
        onDismiss={() => dispatch(clearError())}
        duration={4000}
        action={{
          label: 'Dismiss',
          onPress: () => dispatch(clearError()),
        }}
      >
        {error}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    marginTop: 8,
  },
  loginCard: {
    marginBottom: 20,
  },
  cardContent: {
    padding: 24,
  },
  loginTitle: {
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 16,
    marginLeft: 12,
  },
  loginButton: {
    marginTop: 16,
  },
  buttonContent: {
    height: 48,
  },
  demoCard: {
    marginBottom: 20,
  },
  demoTitle: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  demoSubtitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  demoButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 8,
  },
  demoButton: {
    flex: 1,
  },
});

export default LoginScreen;
