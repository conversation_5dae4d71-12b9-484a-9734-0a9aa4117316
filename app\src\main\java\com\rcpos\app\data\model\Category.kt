package com.rcpos.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "categories")
data class Category(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String?,
    @SerializedName("isActive")
    val isActive: Boolean,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable

@Parcelize
data class CreateCategoryRequest(
    val name: String,
    val description: String?
) : Parcelable

@Parcelize
data class UpdateCategoryRequest(
    val name: String?,
    val description: String?,
    val isActive: Boolean?
) : Parcelable
