import { VALIDATION } from '@/constants';

/**
 * Utility functions for data validation
 */

export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  if (!email.trim()) {
    return { isValid: false, error: 'Email is required' };
  }
  
  if (!VALIDATION.EMAIL_REGEX.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
};

export const validatePassword = (password: string): { isValid: boolean; error?: string } => {
  if (!password.trim()) {
    return { isValid: false, error: 'Password is required' };
  }
  
  if (password.length < VALIDATION.PASSWORD_MIN_LENGTH) {
    return { 
      isValid: false, 
      error: `Password must be at least ${VALIDATION.PASSWORD_MIN_LENGTH} characters long` 
    };
  }
  
  return { isValid: true };
};

export const validateUsername = (username: string): { isValid: boolean; error?: string } => {
  if (!username.trim()) {
    return { isValid: false, error: 'Username is required' };
  }
  
  if (username.length < VALIDATION.USERNAME_MIN_LENGTH) {
    return { 
      isValid: false, 
      error: `Username must be at least ${VALIDATION.USERNAME_MIN_LENGTH} characters long` 
    };
  }
  
  // Check for valid characters (alphanumeric and underscore)
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return { 
      isValid: false, 
      error: 'Username can only contain letters, numbers, and underscores' 
    };
  }
  
  return { isValid: true };
};

export const validateName = (name: string, fieldName: string = 'Name'): { isValid: boolean; error?: string } => {
  if (!name.trim()) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  if (name.length < VALIDATION.NAME_MIN_LENGTH) {
    return { 
      isValid: false, 
      error: `${fieldName} must be at least ${VALIDATION.NAME_MIN_LENGTH} character long` 
    };
  }
  
  return { isValid: true };
};

export const validatePhone = (phone: string): { isValid: boolean; error?: string } => {
  if (!phone.trim()) {
    return { isValid: false, error: 'Phone number is required' };
  }
  
  if (!VALIDATION.PHONE_REGEX.test(phone)) {
    return { isValid: false, error: 'Please enter a valid phone number' };
  }
  
  return { isValid: true };
};

export const validatePrice = (price: number | string): { isValid: boolean; error?: string } => {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  
  if (isNaN(numPrice)) {
    return { isValid: false, error: 'Price must be a valid number' };
  }
  
  if (numPrice < 0) {
    return { isValid: false, error: 'Price cannot be negative' };
  }
  
  if (numPrice === 0) {
    return { isValid: false, error: 'Price must be greater than zero' };
  }
  
  return { isValid: true };
};

export const validateQuantity = (quantity: number | string): { isValid: boolean; error?: string } => {
  const numQuantity = typeof quantity === 'string' ? parseInt(quantity) : quantity;
  
  if (isNaN(numQuantity)) {
    return { isValid: false, error: 'Quantity must be a valid number' };
  }
  
  if (numQuantity < 0) {
    return { isValid: false, error: 'Quantity cannot be negative' };
  }
  
  if (!Number.isInteger(numQuantity)) {
    return { isValid: false, error: 'Quantity must be a whole number' };
  }
  
  return { isValid: true };
};

export const validateRequired = (value: any, fieldName: string): { isValid: boolean; error?: string } => {
  if (value === null || value === undefined || value === '') {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  if (typeof value === 'string' && !value.trim()) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  return { isValid: true };
};

export const validateMinLength = (
  value: string, 
  minLength: number, 
  fieldName: string
): { isValid: boolean; error?: string } => {
  if (value.length < minLength) {
    return { 
      isValid: false, 
      error: `${fieldName} must be at least ${minLength} characters long` 
    };
  }
  
  return { isValid: true };
};

export const validateMaxLength = (
  value: string, 
  maxLength: number, 
  fieldName: string
): { isValid: boolean; error?: string } => {
  if (value.length > maxLength) {
    return { 
      isValid: false, 
      error: `${fieldName} cannot exceed ${maxLength} characters` 
    };
  }
  
  return { isValid: true };
};

export const validatePasswordMatch = (
  password: string, 
  confirmPassword: string
): { isValid: boolean; error?: string } => {
  if (password !== confirmPassword) {
    return { isValid: false, error: 'Passwords do not match' };
  }
  
  return { isValid: true };
};

export const validateForm = (
  data: Record<string, any>, 
  rules: Record<string, Array<(value: any) => { isValid: boolean; error?: string }>>
): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};
  
  Object.keys(rules).forEach(field => {
    const fieldRules = rules[field];
    const value = data[field];
    
    for (const rule of fieldRules) {
      const result = rule(value);
      if (!result.isValid) {
        errors[field] = result.error || 'Invalid value';
        break; // Stop at first error for this field
      }
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
