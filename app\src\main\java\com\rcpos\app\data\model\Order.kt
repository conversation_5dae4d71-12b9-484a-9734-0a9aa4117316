package com.rcpos.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "orders")
data class Order(
    @PrimaryKey
    val id: String,
    @SerializedName("orderNumber")
    val orderNumber: String,
    val status: OrderStatus,
    val subtotal: Double,
    @SerializedName("taxAmount")
    val taxAmount: Double,
    @SerializedName("discountAmount")
    val discountAmount: Double,
    @SerializedName("totalAmount")
    val totalAmount: Double,
    @SerializedName("customerId")
    val customerId: String?,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("orderDate")
    val orderDate: String,
    @SerializedName("completedAt")
    val completedAt: String?,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable {
    val formattedTotal: String
        get() = "₱%.2f".format(totalAmount)
}

enum class OrderStatus {
    @SerializedName("PENDING")
    PENDING,
    @SerializedName("PREPARING")
    PREPARING,
    @SerializedName("READY")
    READY,
    @SerializedName("COMPLETED")
    COMPLETED,
    @SerializedName("CANCELLED")
    CANCELLED
}

@Parcelize
@Entity(tableName = "order_items")
data class OrderItem(
    @PrimaryKey
    val id: String,
    val quantity: Int,
    @SerializedName("unitPrice")
    val unitPrice: Double,
    @SerializedName("totalPrice")
    val totalPrice: Double,
    val notes: String?,
    @SerializedName("orderId")
    val orderId: String,
    @SerializedName("productId")
    val productId: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable {
    val formattedTotal: String
        get() = "₱%.2f".format(totalPrice)
}

@Parcelize
data class OrderItemInput(
    val productId: String,
    val quantity: Int,
    val notes: String? = null
) : Parcelable

@Parcelize
data class CreateOrderRequest(
    val customerId: String? = null,
    val items: List<OrderItemInput>,
    val taxAmount: Double = 0.0,
    val discountAmount: Double = 0.0,
    val notes: String? = null
) : Parcelable

@Parcelize
data class UpdateOrderRequest(
    val status: OrderStatus? = null,
    val customerId: String? = null,
    val taxAmount: Double? = null,
    val discountAmount: Double? = null,
    val notes: String? = null
) : Parcelable

@Parcelize
data class OrderQuery(
    val page: Int = 1,
    val limit: Int = 10,
    val search: String? = null,
    val status: OrderStatus? = null,
    val customerId: String? = null,
    val userId: String? = null,
    val startDate: String? = null,
    val endDate: String? = null,
    val minAmount: Double? = null,
    val maxAmount: Double? = null
) : Parcelable

// Cart item for local cart management
@Parcelize
data class CartItem(
    val product: Product,
    var quantity: Int,
    val notes: String? = null
) : Parcelable {
    val totalPrice: Double
        get() = product.price * quantity
    
    val formattedTotal: String
        get() = "₱%.2f".format(totalPrice)
}
