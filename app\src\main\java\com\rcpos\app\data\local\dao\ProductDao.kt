package com.rcpos.app.data.local.dao

import androidx.room.*
import com.rcpos.app.data.model.Product
import com.rcpos.app.data.model.ProductType
import kotlinx.coroutines.flow.Flow

@Dao
interface ProductDao {
    
    @Query("SELECT * FROM products WHERE isActive = 1 ORDER BY name ASC")
    fun getAllProducts(): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE id = :id")
    suspend fun getProductById(id: String): Product?
    
    @Query("SELECT * FROM products WHERE isActive = 1 AND name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchProducts(query: String): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE categoryId = :categoryId AND isActive = 1 ORDER BY name ASC")
    fun getProductsByCategory(categoryId: String): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE type = :type AND isActive = 1 ORDER BY name ASC")
    fun getProductsByType(type: ProductType): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE trackInventory = 1 AND stockQuantity <= minStockLevel AND isActive = 1 ORDER BY stockQuantity ASC")
    fun getLowStockProducts(): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE barcode = :barcode AND isActive = 1 LIMIT 1")
    suspend fun getProductByBarcode(barcode: String): Product?
    
    @Query("SELECT * FROM products WHERE sku = :sku AND isActive = 1 LIMIT 1")
    suspend fun getProductBySku(sku: String): Product?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: Product)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProducts(products: List<Product>)
    
    @Update
    suspend fun updateProduct(product: Product)
    
    @Delete
    suspend fun deleteProduct(product: Product)
    
    @Query("DELETE FROM products WHERE id = :id")
    suspend fun deleteProductById(id: String)
    
    @Query("DELETE FROM products")
    suspend fun deleteAllProducts()
    
    @Query("UPDATE products SET stockQuantity = :quantity WHERE id = :id")
    suspend fun updateStock(id: String, quantity: Int)
    
    @Query("SELECT COUNT(*) FROM products WHERE isActive = 1")
    suspend fun getProductCount(): Int
    
    @Query("SELECT COUNT(*) FROM products WHERE trackInventory = 1 AND stockQuantity <= minStockLevel AND isActive = 1")
    suspend fun getLowStockCount(): Int
}
