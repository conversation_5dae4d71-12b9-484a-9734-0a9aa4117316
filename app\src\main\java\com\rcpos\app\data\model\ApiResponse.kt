package com.rcpos.app.data.model

import com.google.gson.annotations.SerializedName

/**
 * Generic API response wrapper that matches the backend response format
 */
data class ApiResponse<T>(
    val success: Boolean,
    val message: String,
    val data: T? = null,
    val error: String? = null
)

/**
 * Paginated response for list endpoints
 */
data class PaginatedResponse<T>(
    val items: List<T>,
    val pagination: PaginationInfo
)

data class PaginationInfo(
    val page: Int,
    val limit: Int,
    val total: Int,
    @SerializedName("totalPages")
    val totalPages: Int,
    @SerializedName("hasNext")
    val hasNext: Boolean,
    @SerializedName("hasPrev")
    val hasPrev: Boolean
)

/**
 * Error response for API failures
 */
data class ErrorResponse(
    val success: Boolean = false,
    val message: String,
    val errors: List<ValidationError>? = null
)

data class ValidationError(
    val field: String,
    val message: String
)

/**
 * Network result wrapper for handling API responses
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String, val code: Int? = null) : NetworkResult<T>()
    data class Loading<T>(val isLoading: Boolean = true) : NetworkResult<T>()
}

/**
 * Resource wrapper for UI state management
 */
sealed class Resource<T> {
    data class Success<T>(val data: T) : Resource<T>()
    data class Error<T>(val message: String, val data: T? = null) : Resource<T>()
    data class Loading<T>(val data: T? = null) : Resource<T>()
}

/**
 * UI state for different screens
 */
data class UiState<T>(
    val isLoading: Boolean = false,
    val data: T? = null,
    val error: String? = null,
    val isRefreshing: Boolean = false
) {
    val isSuccess: Boolean get() = data != null && error == null
    val isEmpty: Boolean get() = data == null && error == null && !isLoading
}

/**
 * Common constants
 */
object ApiConstants {
    const val DEFAULT_PAGE_SIZE = 20
    const val MAX_PAGE_SIZE = 100
    const val TIMEOUT_SECONDS = 30L
    
    // Header keys
    const val HEADER_AUTHORIZATION = "Authorization"
    const val HEADER_CONTENT_TYPE = "Content-Type"
    const val HEADER_ACCEPT = "Accept"
    
    // Content types
    const val CONTENT_TYPE_JSON = "application/json"
    
    // Auth
    const val BEARER_PREFIX = "Bearer "
}
