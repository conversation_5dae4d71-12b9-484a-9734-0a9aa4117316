package com.rcpos.app.data.repository

import com.rcpos.app.data.api.AuthApiService
import com.rcpos.app.data.local.dao.UserDao
import com.rcpos.app.data.local.preferences.AuthPreferences
import com.rcpos.app.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val authApiService: AuthApiService,
    private val authPreferences: AuthPreferences,
    private val userDao: UserDao
) {
    
    fun isLoggedIn(): Flow<Boolean> = authPreferences.isLoggedIn()
    
    fun getCurrentUser(): Flow<User?> = authPreferences.getUser()
    
    suspend fun login(email: String, password: String): NetworkResult<AuthResponse> {
        return try {
            val request = LoginRequest(email, password)
            val response = authApiService.login(request)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    val authData = apiResponse.data
                    
                    // Save auth data to preferences
                    authPreferences.saveAuthData(
                        token = authData.token,
                        refreshToken = authData.refreshToken,
                        user = authData.user
                    )
                    
                    // Cache user data locally
                    userDao.insertUser(authData.user)
                    
                    NetworkResult.Success(authData)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Login failed")
                }
            } else {
                NetworkResult.Error("Login failed: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun register(
        email: String,
        username: String,
        password: String,
        firstName: String,
        lastName: String,
        role: UserRole = UserRole.CASHIER
    ): NetworkResult<User> {
        return try {
            val request = RegisterRequest(email, username, password, firstName, lastName, role)
            val response = authApiService.register(request)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    NetworkResult.Success(apiResponse.data)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Registration failed")
                }
            } else {
                NetworkResult.Error("Registration failed: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun changePassword(
        currentPassword: String,
        newPassword: String,
        confirmPassword: String
    ): NetworkResult<Unit> {
        return try {
            val request = ChangePasswordRequest(currentPassword, newPassword, confirmPassword)
            val response = authApiService.changePassword(request)
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true) {
                    NetworkResult.Success(Unit)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Password change failed")
                }
            } else {
                NetworkResult.Error("Password change failed: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun refreshToken(): NetworkResult<AuthResponse> {
        return try {
            val response = authApiService.refreshToken()
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    val authData = apiResponse.data
                    
                    // Update token in preferences
                    authPreferences.updateToken(
                        token = authData.token,
                        refreshToken = authData.refreshToken
                    )
                    
                    NetworkResult.Success(authData)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Token refresh failed")
                }
            } else {
                NetworkResult.Error("Token refresh failed: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun getProfile(): NetworkResult<User> {
        return try {
            val response = authApiService.getProfile()
            
            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    val user = apiResponse.data
                    
                    // Update user data in preferences and local database
                    authPreferences.updateUser(user)
                    userDao.insertUser(user)
                    
                    NetworkResult.Success(user)
                } else {
                    NetworkResult.Error(apiResponse?.message ?: "Failed to get profile")
                }
            } else {
                NetworkResult.Error("Failed to get profile: ${response.message()}")
            }
        } catch (e: Exception) {
            NetworkResult.Error("Network error: ${e.message}")
        }
    }
    
    suspend fun logout() {
        authPreferences.clearAuthData()
        // Optionally clear local database or keep for offline access
    }
    
    suspend fun getCurrentUserFromCache(): User? {
        return authPreferences.getUser().first()
    }
}
