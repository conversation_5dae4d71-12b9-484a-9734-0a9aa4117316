{"expo": {"name": "RC POS", "slug": "rcpos-expo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.rcpos.app", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to scan barcodes for product identification.", "NSMicrophoneUsageDescription": "This app may use the microphone for barcode scanning functionality."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.rcpos.app", "permissions": ["android.permission.CAMERA", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", ["expo-barcode-scanner", {"cameraPermission": "Allow RC POS to access camera for barcode scanning."}], ["expo-camera", {"cameraPermission": "Allow RC POS to access camera for barcode scanning."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "your-project-id-here"}}}}