import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  IconButton,
  Divider,
  useTheme
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';

import { RootState, AppDispatch } from '@/store';
import { removeFromCart, updateQuantity, clearCart } from '@/store/slices/cartSlice';
import { CartItem } from '@/types';
import { DEFAULTS } from '@/constants';

const CartScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  
  const { items, subtotal, taxAmount, total, itemCount } = useSelector((state: RootState) => state.cart);

  const handleRemoveItem = (productId: string) => {
    dispatch(removeFromCart(productId));
  };

  const handleUpdateQuantity = (productId: string, quantity: number) => {
    dispatch(updateQuantity({ productId, quantity }));
  };

  const handleClearCart = () => {
    dispatch(clearCart());
  };

  const handleCheckout = () => {
    // TODO: Navigate to checkout screen
    console.log('Proceeding to checkout...');
  };

  const renderCartItem = ({ item }: { item: CartItem }) => (
    <Card style={styles.itemCard}>
      <Card.Content>
        <View style={styles.itemHeader}>
          <Text variant="titleMedium" style={styles.itemName}>
            {item.product.name}
          </Text>
          <IconButton
            icon="delete"
            size={20}
            onPress={() => handleRemoveItem(item.product.id)}
            style={styles.deleteButton}
          />
        </View>
        
        <Text variant="bodyMedium" style={[styles.itemPrice, { color: theme.colors.primary }]}>
          {DEFAULTS.CURRENCY}{item.product.price.toFixed(2)} each
        </Text>
        
        {item.notes && (
          <Text variant="bodySmall" style={[styles.notes, { color: theme.colors.onSurfaceVariant }]}>
            Notes: {item.notes}
          </Text>
        )}
        
        <View style={styles.quantityRow}>
          <View style={styles.quantityControls}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
              disabled={item.quantity <= 1}
              style={styles.quantityButton}
            />
            <Text variant="titleMedium" style={styles.quantity}>
              {item.quantity}
            </Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
              style={styles.quantityButton}
            />
          </View>
          
          <Text variant="titleMedium" style={[styles.itemTotal, { color: theme.colors.primary }]}>
            {DEFAULTS.CURRENCY}{(item.product.price * item.quantity).toFixed(2)}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  if (items.length === 0) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.emptyContainer}>
          <Text variant="headlineSmall" style={[styles.emptyTitle, { color: theme.colors.onSurfaceVariant }]}>
            Your cart is empty
          </Text>
          <Text variant="bodyMedium" style={[styles.emptySubtitle, { color: theme.colors.onSurfaceVariant }]}>
            Add some products to get started
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.headerTitle}>
            Cart ({itemCount} items)
          </Text>
          <Button
            mode="text"
            onPress={handleClearCart}
            textColor={theme.colors.error}
          >
            Clear All
          </Button>
        </View>
        
        <FlatList
          data={items}
          renderItem={renderCartItem}
          keyExtractor={(item) => item.product.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
        
        <Card style={styles.summaryCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.summaryTitle}>
              Order Summary
            </Text>
            
            <View style={styles.summaryRow}>
              <Text variant="bodyMedium">Subtotal</Text>
              <Text variant="bodyMedium">
                {DEFAULTS.CURRENCY}{subtotal.toFixed(2)}
              </Text>
            </View>
            
            <View style={styles.summaryRow}>
              <Text variant="bodyMedium">Tax ({(DEFAULTS.TAX_RATE * 100).toFixed(0)}%)</Text>
              <Text variant="bodyMedium">
                {DEFAULTS.CURRENCY}{taxAmount.toFixed(2)}
              </Text>
            </View>
            
            <Divider style={styles.divider} />
            
            <View style={styles.summaryRow}>
              <Text variant="titleMedium" style={styles.totalLabel}>
                Total
              </Text>
              <Text variant="titleMedium" style={[styles.totalAmount, { color: theme.colors.primary }]}>
                {DEFAULTS.CURRENCY}{total.toFixed(2)}
              </Text>
            </View>
            
            <Button
              mode="contained"
              onPress={handleCheckout}
              style={styles.checkoutButton}
              contentStyle={styles.checkoutButtonContent}
            >
              Proceed to Checkout
            </Button>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontWeight: 'bold',
  },
  listContent: {
    paddingBottom: 16,
  },
  itemCard: {
    marginBottom: 12,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemName: {
    flex: 1,
    fontWeight: 'bold',
    marginRight: 8,
  },
  deleteButton: {
    margin: 0,
  },
  itemPrice: {
    marginBottom: 4,
  },
  notes: {
    marginBottom: 8,
    fontStyle: 'italic',
  },
  quantityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    margin: 0,
  },
  quantity: {
    marginHorizontal: 16,
    minWidth: 30,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  itemTotal: {
    fontWeight: 'bold',
  },
  summaryCard: {
    marginTop: 16,
  },
  summaryTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  divider: {
    marginVertical: 12,
  },
  totalLabel: {
    fontWeight: 'bold',
  },
  totalAmount: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  checkoutButton: {
    marginTop: 16,
  },
  checkoutButtonContent: {
    height: 48,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtitle: {
    textAlign: 'center',
  },
});

export default CartScreen;
