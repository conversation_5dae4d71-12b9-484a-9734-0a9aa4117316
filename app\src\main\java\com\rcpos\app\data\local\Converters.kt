package com.rcpos.app.data.local

import androidx.room.TypeConverter
import com.rcpos.app.data.model.*

class Converters {
    
    @TypeConverter
    fun fromUserRole(role: UserRole): String = role.name
    
    @TypeConverter
    fun toUserRole(role: String): UserRole = UserRole.valueOf(role)
    
    @TypeConverter
    fun fromProductType(type: ProductType): String = type.name
    
    @TypeConverter
    fun toProductType(type: String): ProductType = ProductType.valueOf(type)
    
    @TypeConverter
    fun fromOrderStatus(status: OrderStatus): String = status.name
    
    @TypeConverter
    fun toOrderStatus(status: String): OrderStatus = OrderStatus.valueOf(status)
    
    @TypeConverter
    fun fromPaymentMethod(method: PaymentMethod): String = method.name
    
    @TypeConverter
    fun toPaymentMethod(method: String): PaymentMethod = PaymentMethod.valueOf(method)
    
    @TypeConverter
    fun fromPaymentStatus(status: PaymentStatus): String = status.name
    
    @TypeConverter
    fun toPaymentStatus(status: String): PaymentStatus = PaymentStatus.valueOf(status)
}
