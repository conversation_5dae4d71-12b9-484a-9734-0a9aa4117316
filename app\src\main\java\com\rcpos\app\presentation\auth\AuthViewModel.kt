package com.rcpos.app.presentation.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.rcpos.app.data.model.NetworkResult
import com.rcpos.app.data.model.UiState
import com.rcpos.app.data.model.User
import com.rcpos.app.data.model.UserRole
import com.rcpos.app.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _loginState = MutableStateFlow(UiState<User>())
    val loginState: StateFlow<UiState<User>> = _loginState.asStateFlow()
    
    private val _registerState = MutableStateFlow(UiState<User>())
    val registerState: StateFlow<UiState<User>> = _registerState.asStateFlow()
    
    val isLoggedIn = authRepository.isLoggedIn()
    val currentUser = authRepository.getCurrentUser()
    
    fun login(email: String, password: String) {
        viewModelScope.launch {
            _loginState.value = UiState(isLoading = true)
            
            when (val result = authRepository.login(email, password)) {
                is NetworkResult.Success -> {
                    _loginState.value = UiState(data = result.data.user)
                }
                is NetworkResult.Error -> {
                    _loginState.value = UiState(error = result.message)
                }
                is NetworkResult.Loading -> {
                    _loginState.value = UiState(isLoading = true)
                }
            }
        }
    }
    
    fun register(
        email: String,
        username: String,
        password: String,
        firstName: String,
        lastName: String,
        role: UserRole = UserRole.CASHIER
    ) {
        viewModelScope.launch {
            _registerState.value = UiState(isLoading = true)
            
            when (val result = authRepository.register(email, username, password, firstName, lastName, role)) {
                is NetworkResult.Success -> {
                    _registerState.value = UiState(data = result.data)
                }
                is NetworkResult.Error -> {
                    _registerState.value = UiState(error = result.message)
                }
                is NetworkResult.Loading -> {
                    _registerState.value = UiState(isLoading = true)
                }
            }
        }
    }
    
    fun logout() {
        viewModelScope.launch {
            authRepository.logout()
        }
    }
    
    fun clearLoginState() {
        _loginState.value = UiState()
    }
    
    fun clearRegisterState() {
        _registerState.value = UiState()
    }
}
