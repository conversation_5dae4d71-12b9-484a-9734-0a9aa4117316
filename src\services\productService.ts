import { apiClient } from './apiClient';
import { Product, PaginatedResponse, ProductType } from '@/types';

interface ProductQuery {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: string;
  type?: ProductType;
  isActive?: boolean;
  lowStock?: boolean;
}

interface CreateProductRequest {
  name: string;
  description?: string;
  price: number;
  cost?: number;
  sku?: string;
  barcode?: string;
  type: ProductType;
  categoryId: string;
  stockQuantity?: number;
  minStockLevel?: number;
  trackInventory?: boolean;
  preparationTime?: number;
  calories?: number;
  allergens?: string;
}

interface UpdateProductRequest extends Partial<CreateProductRequest> {
  isActive?: boolean;
}

interface UpdateStockRequest {
  quantity: number;
  type: 'ADD' | 'SUBTRACT' | 'SET';
  reason?: string;
}

class ProductService {
  async getProducts(query: ProductQuery = {}): Promise<PaginatedResponse<Product>> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    
    return apiClient.get<PaginatedResponse<Product>>(`/products?${params.toString()}`);
  }

  async getProductById(id: string): Promise<Product> {
    return apiClient.get<Product>(`/products/${id}`);
  }

  async createProduct(data: CreateProductRequest): Promise<Product> {
    return apiClient.post<Product>('/products', data);
  }

  async updateProduct(id: string, data: UpdateProductRequest): Promise<Product> {
    return apiClient.put<Product>(`/products/${id}`, data);
  }

  async deleteProduct(id: string): Promise<void> {
    return apiClient.delete<void>(`/products/${id}`);
  }

  async updateStock(id: string, data: UpdateStockRequest): Promise<Product> {
    return apiClient.put<Product>(`/products/${id}/stock`, data);
  }

  async searchProducts(query: string): Promise<Product[]> {
    const response = await this.getProducts({ search: query, limit: 50 });
    return response.items;
  }

  async getProductsByCategory(categoryId: string): Promise<Product[]> {
    const response = await this.getProducts({ categoryId, limit: 100 });
    return response.items;
  }

  async getLowStockProducts(): Promise<Product[]> {
    const response = await this.getProducts({ lowStock: true, limit: 100 });
    return response.items;
  }
}

export const productService = new ProductService();
