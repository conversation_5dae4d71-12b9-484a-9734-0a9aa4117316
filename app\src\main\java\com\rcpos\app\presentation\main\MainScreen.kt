package com.rcpos.app.presentation.main

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.rcpos.app.presentation.cart.CartScreen
import com.rcpos.app.presentation.dashboard.DashboardScreen
import com.rcpos.app.presentation.inventory.InventoryScreen
import com.rcpos.app.presentation.orders.OrdersScreen
import com.rcpos.app.presentation.products.ProductsScreen

sealed class BottomNavItem(
    val route: String,
    val title: String,
    val icon: ImageVector
) {
    object Dashboard : BottomNavItem("dashboard", "Dashboard", Icons.Default.Dashboard)
    object Products : BottomNavItem("products", "Products", Icons.Default.Inventory)
    object Cart : BottomNavItem("cart", "Cart", Icons.Default.ShoppingCart)
    object Orders : BottomNavItem("orders", "Orders", Icons.Default.Receipt)
    object Inventory : BottomNavItem("inventory", "Inventory", Icons.Default.Storage)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    onLogout: () -> Unit
) {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination
    
    val bottomNavItems = listOf(
        BottomNavItem.Dashboard,
        BottomNavItem.Products,
        BottomNavItem.Cart,
        BottomNavItem.Orders,
        BottomNavItem.Inventory
    )
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when (currentDestination?.route) {
                            "dashboard" -> "Dashboard"
                            "products" -> "Products"
                            "cart" -> "Shopping Cart"
                            "orders" -> "Orders"
                            "inventory" -> "Inventory"
                            else -> "RC POS"
                        }
                    )
                },
                actions = {
                    IconButton(onClick = onLogout) {
                        Icon(Icons.Default.Logout, contentDescription = "Logout")
                    }
                }
            )
        },
        bottomBar = {
            NavigationBar {
                bottomNavItems.forEach { item ->
                    NavigationBarItem(
                        icon = { Icon(item.icon, contentDescription = item.title) },
                        label = { Text(item.title) },
                        selected = currentDestination?.hierarchy?.any { it.route == item.route } == true,
                        onClick = {
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = BottomNavItem.Dashboard.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(BottomNavItem.Dashboard.route) {
                DashboardScreen()
            }
            composable(BottomNavItem.Products.route) {
                ProductsScreen()
            }
            composable(BottomNavItem.Cart.route) {
                CartScreen()
            }
            composable(BottomNavItem.Orders.route) {
                OrdersScreen()
            }
            composable(BottomNavItem.Inventory.route) {
                InventoryScreen()
            }
        }
    }
}
