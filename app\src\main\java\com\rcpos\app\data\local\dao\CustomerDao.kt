package com.rcpos.app.data.local.dao

import androidx.room.*
import com.rcpos.app.data.model.Customer
import kotlinx.coroutines.flow.Flow

@Dao
interface CustomerDao {
    
    @Query("SELECT * FROM customers WHERE isActive = 1 ORDER BY firstName ASC")
    fun getAllCustomers(): Flow<List<Customer>>
    
    @Query("SELECT * FROM customers WHERE id = :id")
    suspend fun getCustomerById(id: String): Customer?
    
    @Query("SELECT * FROM customers WHERE email = :email")
    suspend fun getCustomerByEmail(email: String): Customer?
    
    @Query("SELECT * FROM customers WHERE phone = :phone")
    suspend fun getCustomerByPhone(phone: String): Customer?
    
    @Query("""
        SELECT * FROM customers 
        WHERE isActive = 1 AND (
            firstName LIKE '%' || :query || '%' OR 
            lastName LIKE '%' || :query || '%' OR 
            email LIKE '%' || :query || '%' OR 
            phone LIKE '%' || :query || '%'
        ) 
        ORDER BY firstName ASC
    """)
    fun searchCustomers(query: String): Flow<List<Customer>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomer(customer: Customer)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomers(customers: List<Customer>)
    
    @Update
    suspend fun updateCustomer(customer: Customer)
    
    @Delete
    suspend fun deleteCustomer(customer: Customer)
    
    @Query("DELETE FROM customers WHERE id = :id")
    suspend fun deleteCustomerById(id: String)
    
    @Query("DELETE FROM customers")
    suspend fun deleteAllCustomers()
    
    @Query("SELECT COUNT(*) FROM customers WHERE isActive = 1")
    suspend fun getCustomerCount(): Int
}
